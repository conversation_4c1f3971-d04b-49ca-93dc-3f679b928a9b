# Database Configuration
spring.datasource.url=*********************************************************************************************************
spring.datasource.username=root
spring.datasource.password=020924
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA/Hibernate Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.properties.hibernate.format_sql=true

# Server Configuration
server.port=8080

# JWT Configuration
jwt.secret=hospitalSystemSecretKey
jwt.expiration=86400000

# Logging Configuration
logging.level.org.springframework.security=DEBUG
logging.level.com.hospital.system=DEBUG 