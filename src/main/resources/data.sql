-- 初始化管理员、医生和患者账户
INSERT INTO users (username, password, email, full_name, phone_number, role, is_active) VALUES
-- 管理员账户 (密码: admin123)
('admin', '$2a$10$3Qk2EBuidvQJLnQwJYh.7OAMxqTVsvuq7tnhgd5.yULAaOY8LzKOK', '<EMAIL>', '系统管理员', '13800000000', 'ADMIN', TRUE),

-- 医生账户 (密码: doctor123)
('drwang', '$2a$10$3Qk2EBuidvQJLnQwJYh.7OAMxqTVsvuq7tnhgd5.yULAaOY8LzKOK', '<EMAIL>', '王医生', '13800000001', 'DOCTOR', TRUE),
('drli', '$2a$10$3Qk2EBuidvQJLnQwJYh.7OAMxqTVsvuq7tnhgd5.yULAaOY8LzKOK', '<EMAIL>', '李医生', '13800000002', 'DOCTOR', TRUE),
('drzhang', '$2a$10$3Qk2EBuidvQJLnQwJYh.7OAMxqTVsvuq7tnhgd5.yULAaOY8LzKOK', '<EMAIL>', '张医生', '13800000003', 'DOCTOR', TRUE),
('drchen', '$2a$10$3Qk2EBuidvQJLnQwJYh.7OAMxqTVsvuq7tnhgd5.yULAaOY8LzKOK', '<EMAIL>', '陈医生', '13800000004', 'DOCTOR', TRUE),
('drzhou', '$2a$10$3Qk2EBuidvQJLnQwJYh.7OAMxqTVsvuq7tnhgd5.yULAaOY8LzKOK', '<EMAIL>', '周医生', '13800000005', 'DOCTOR', TRUE),

-- 患者账户 (密码: patient123)
('patient1', '$2a$10$3Qk2EBuidvQJLnQwJYh.7OAMxqTVsvuq7tnhgd5.yULAaOY8LzKOK', '<EMAIL>', '张三', '13900000001', 'PATIENT', TRUE),
('patient2', '$2a$10$3Qk2EBuidvQJLnQwJYh.7OAMxqTVsvuq7tnhgd5.yULAaOY8LzKOK', '<EMAIL>', '李四', '13900000002', 'PATIENT', TRUE),
('patient3', '$2a$10$3Qk2EBuidvQJLnQwJYh.7OAMxqTVsvuq7tnhgd5.yULAaOY8LzKOK', '<EMAIL>', '王五', '13900000003', 'PATIENT', TRUE),
('patient4', '$2a$10$3Qk2EBuidvQJLnQwJYh.7OAMxqTVsvuq7tnhgd5.yULAaOY8LzKOK', '<EMAIL>', '赵六', '13900000004', 'PATIENT', TRUE);

-- 初始化科室数据
INSERT INTO departments (name, description, floor_number, room_number) VALUES
('心脏内科', '专注于心脏和心血管系统疾病的诊断、治疗和预防', 2, '201-210'),
('神经内科', '专注于神经系统疾病的诊断和治疗', 3, '301-310'),
('儿科', '专注于儿童健康和儿科疾病', 1, '101-110'),
('骨科', '专注于骨骼、关节、肌肉等运动系统疾病', 4, '401-410'),
('消化内科', '专注于消化系统疾病的诊断和治疗', 2, '211-220');

-- 初始化患者数据
INSERT INTO patients (user_id, medical_history, birth_date, gender, address, emergency_contact) VALUES
(7, '无重大病史', '1985-05-15', '男', '北京市海淀区XX路XX号', '紧急联系人: 13988888888'),
(8, '糖尿病史', '1978-09-20', '女', '上海市浦东新区XX路XX号', '紧急联系人: 13977777777'),
(9, '高血压', '1990-03-10', '男', '广州市天河区XX路XX号', '紧急联系人: 13966666666'),
(10, '无重大病史', '1982-11-28', '女', '深圳市南山区XX路XX号', '紧急联系人: 13955555555');

-- 初始化医生数据
INSERT INTO doctors (user_id, department_id, specialization, license_number, qualification, years_of_experience, rating_score, rating_count) VALUES
(2, 1, '心脏病学', 'DOC20210001', '主任医师，医学博士', 15, 4.8, 120),
(3, 2, '神经病学', 'DOC20210002', '副主任医师，医学博士', 12, 4.7, 95),
(4, 3, '儿科学', 'DOC20210003', '主治医师，医学硕士', 8, 4.5, 75),
(5, 4, '骨科学', 'DOC20210004', '主任医师，医学博士', 20, 4.9, 150),
(6, 5, '消化病学', 'DOC20210005', '副主任医师，医学硕士', 10, 4.6, 85);

-- 初始化排班数据
INSERT INTO schedules (doctor_id, day_of_week, start_time, end_time, appointment_duration, max_appointments) VALUES
-- 王医生的排班
(1, 'MONDAY', '08:00:00', '12:00:00', 30, 8),
(1, 'WEDNESDAY', '08:00:00', '12:00:00', 30, 8),
(1, 'FRIDAY', '14:00:00', '18:00:00', 30, 8),

-- 李医生的排班
(2, 'MONDAY', '14:00:00', '18:00:00', 30, 8),
(2, 'THURSDAY', '08:00:00', '12:00:00', 30, 8),
(2, 'SATURDAY', '08:00:00', '12:00:00', 30, 8),

-- 张医生的排班
(3, 'TUESDAY', '08:00:00', '12:00:00', 30, 8),
(3, 'THURSDAY', '14:00:00', '18:00:00', 30, 8),
(3, 'SATURDAY', '14:00:00', '18:00:00', 30, 8),

-- 陈医生的排班
(4, 'TUESDAY', '14:00:00', '18:00:00', 30, 8),
(4, 'WEDNESDAY', '14:00:00', '18:00:00', 30, 8),
(4, 'FRIDAY', '08:00:00', '12:00:00', 30, 8),

-- 周医生的排班
(5, 'MONDAY', '08:00:00', '12:00:00', 30, 8),
(5, 'WEDNESDAY', '14:00:00', '18:00:00', 30, 8),
(5, 'SATURDAY', '08:00:00', '12:00:00', 30, 8);

-- 初始化预约数据
INSERT INTO appointments (patient_id, doctor_id, schedule_id, appointment_date, start_time, end_time, appointment_number, status, symptoms, diagnosis_notes, created_at) VALUES
-- 张三的预约
(1, 1, 1, '2025-06-25', '09:00:00', '09:30:00', 'APP202506250001', 'COMPLETED', '胸痛，心悸', '初步判断为心肌缺血，建议进一步检查', '2025-06-20'),
(1, 3, 7, '2025-06-27', '10:00:00', '10:30:00', 'APP202506270001', 'SCHEDULED', '孩子发热，咳嗽', NULL, '2025-06-22'),

-- 李四的预约
(2, 2, 4, '2025-06-26', '15:00:00', '15:30:00', 'APP202506260001', 'COMPLETED', '头痛，头晕', '可能是颈椎病引起的头痛，建议CT检查', '2025-06-21'),
(2, 5, 13, '2025-06-29', '09:00:00', '09:30:00', 'APP202506290001', 'SCHEDULED', '腹痛，消化不良', NULL, '2025-06-24'),

-- 王五的预约
(3, 4, 10, '2025-06-25', '16:00:00', '16:30:00', 'APP202506250002', 'COMPLETED', '腰痛，行走不便', '腰椎间盘突出，建议保守治疗', '2025-06-20'),
(3, 1, 3, '2025-06-30', '15:00:00', '15:30:00', 'APP202506300001', 'SCHEDULED', '心慌，气短', NULL, '2025-06-25'),

-- 赵六的预约
(4, 3, 8, '2025-06-26', '16:00:00', '16:30:00', 'APP202506260002', 'COMPLETED', '孩子咳嗽，有痰', '支气管炎，已开具处方', '2025-06-21'),
(4, 2, 5, '2025-06-30', '10:00:00', '10:30:00', 'APP202506300002', 'SCHEDULED', '头晕，记忆力下降', NULL, '2025-06-25');

-- 初始化医疗报告数据
INSERT INTO medical_reports (appointment_id, report_type, report_date, results, file_path) VALUES
(1, '心电图', '2025-06-25', '心电图结果显示ST段轻度抬高，建议进行冠状动脉造影检查', '/reports/ecg-20250625-001.pdf'),
(2, '血常规', '2025-06-26', '白细胞计数轻度升高，可能存在感染，建议抗感染治疗', '/reports/blood-20250626-001.pdf'),
(3, '头颅CT', '2025-06-26', '未见明显异常，颈椎有轻度退行性变', '/reports/ct-20250626-001.pdf'),
(4, 'X光', '2025-06-25', '腰椎4-5椎间隙变窄，椎间盘突出', '/reports/xray-20250625-001.pdf'),
(5, '咽拭子检查', '2025-06-26', '检出轻度细菌感染，对常用抗生素敏感', '/reports/throat-20250626-001.pdf'); 