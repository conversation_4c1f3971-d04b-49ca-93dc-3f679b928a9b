package com.hospital.system.service;

import com.hospital.system.dto.StatsResponse;

import java.time.LocalDate;
import java.time.Month;
import java.util.Map;

public interface StatisticsService {
    StatsResponse getSystemStatistics();
    
    StatsResponse getDoctorWorkloadStatistics(LocalDate startDate, LocalDate endDate);
    
    StatsResponse getDepartmentPopularityStatistics(LocalDate startDate, LocalDate endDate);
    
    StatsResponse getDoctorStatistics(Long doctorId);
    
    StatsResponse getDepartmentStatistics(Long departmentId);
    
    Map<String, Integer> getMonthlyAppointmentStatistics(int year);
    
    Map<String, Integer> getMonthlyAppointmentStatisticsByDoctor(Long doctorId, int year);
    
    Map<String, Integer> getMonthlyAppointmentStatisticsByDepartment(Long departmentId, int year);
} 