package com.hospital.system.service;

import com.hospital.system.dto.ScheduleDTO;
import com.hospital.system.model.Schedule;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.List;

public interface ScheduleService {
    ScheduleDTO getScheduleById(Long id);
    
    List<ScheduleDTO> getAllSchedules();
    
    List<ScheduleDTO> getSchedulesByDoctor(Long doctorId);
    
    List<ScheduleDTO> getSchedulesByDoctorAndDay(Long doctorId, DayOfWeek dayOfWeek);
    
    List<ScheduleDTO> getSchedulesByDepartment(Long departmentId);
    
    List<ScheduleDTO> getAvailableSchedules(Long doctorId, LocalDate date);
    
    ScheduleDTO createSchedule(ScheduleDTO scheduleDTO);
    
    List<ScheduleDTO> createBatchSchedules(List<ScheduleDTO> schedules);
    
    ScheduleDTO updateSchedule(Long id, ScheduleDTO scheduleDTO);
    
    void deleteSchedule(Long id);
    
    Schedule getScheduleEntityById(Long id);
} 