package com.hospital.system.service;

import com.hospital.system.dto.MedicalReportDTO;
import com.hospital.system.model.MedicalReport;

import java.time.LocalDate;
import java.util.List;

public interface MedicalReportService {
    MedicalReportDTO getMedicalReportById(Long id);
    
    List<MedicalReportDTO> getAllMedicalReports();
    
    List<MedicalReportDTO> getMedicalReportsByPatient(Long patientId);
    
    List<MedicalReportDTO> getMedicalReportsByCurrentPatient();
    
    List<MedicalReportDTO> getMedicalReportsByDoctor(Long doctorId);
    
    List<MedicalReportDTO> getMedicalReportsByAppointment(Long appointmentId);
    
    List<MedicalReportDTO> getMedicalReportsByDateRange(LocalDate startDate, LocalDate endDate);
    
    MedicalReportD<PERSON> createMedicalReport(MedicalReportDTO medicalReportDTO);
    
    MedicalReportDTO updateMedicalReport(Long id, MedicalReportDTO medicalReportDTO);
    
    void deleteMedicalReport(Long id);
    
    MedicalReport getMedicalReportEntityById(Long id);
} 