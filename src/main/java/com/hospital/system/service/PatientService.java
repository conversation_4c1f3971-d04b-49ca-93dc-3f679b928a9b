package com.hospital.system.service;

import com.hospital.system.dto.PatientDTO;
import com.hospital.system.model.Patient;
import com.hospital.system.model.User;

import java.util.List;

public interface PatientService {
    PatientDTO getPatientById(Long id);
    
    PatientDTO getPatientByUserId(Long userId);
    
    PatientDTO getCurrentPatient();
    
    PatientDTO createPatient(PatientDTO patientDTO, User user);
    
    PatientDTO updatePatient(Long id, PatientDTO patientDTO);
    
    List<PatientDTO> getAllPatients();
    
    void deletePatient(Long id);
    
    Patient getPatientEntityById(Long id);
    
    Patient getPatientEntityByUser(User user);
} 