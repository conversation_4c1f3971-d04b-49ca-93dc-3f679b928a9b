package com.hospital.system.service;

import com.hospital.system.dto.AppointmentDTO;
import com.hospital.system.dto.AppointmentRequest;
import com.hospital.system.dto.DiagnosisRequest;
import com.hospital.system.model.Appointment;
import com.hospital.system.model.AppointmentStatus;
import com.hospital.system.model.MedicalReport;

import java.time.LocalDate;
import java.util.List;

public interface AppointmentService {
    AppointmentDTO getAppointmentById(Long id);
    
    AppointmentDTO getAppointmentByNumber(String appointmentNumber);
    
    List<AppointmentDTO> getAllAppointments();
    
    List<AppointmentDTO> getAppointmentsByPatient(Long patientId);
    
    List<AppointmentDTO> getCurrentPatientAppointments();
    
    List<AppointmentDTO> getAppointmentsByDoctor(Long doctorId);
    
    List<AppointmentDTO> getCurrentDoctorAppointments();
    
    List<AppointmentDTO> getAppointmentsByDoctorAndDate(Long doctorId, LocalDate date);
    
    List<AppointmentDTO> getTodayAppointmentsForCurrentDoctor();
    
    List<AppointmentDTO> getAppointmentsByDepartment(Long departmentId);
    
    List<AppointmentDTO> getAppointmentsByStatus(AppointmentStatus status);
    
    List<AppointmentDTO> getAppointmentsByDateRange(LocalDate startDate, LocalDate endDate);
    
    AppointmentDTO bookAppointment(AppointmentRequest appointmentRequest);
    
    AppointmentDTO updateAppointmentStatus(Long id, AppointmentStatus status);
    
    AppointmentDTO updateAppointment(Long id, AppointmentDTO appointmentDTO);
    
    AppointmentDTO submitDiagnosis(DiagnosisRequest diagnosisRequest);
    
    MedicalReport createMedicalReport(Long appointmentId, String reportType, String results);
    
    void cancelAppointment(Long id);
    
    void deleteAppointment(Long id);
    
    String generateAppointmentNumber();
    
    boolean isTimeSlotAvailable(Long doctorId, LocalDate date, java.time.LocalTime startTime, java.time.LocalTime endTime);
    
    Appointment getAppointmentEntityById(Long id);
} 