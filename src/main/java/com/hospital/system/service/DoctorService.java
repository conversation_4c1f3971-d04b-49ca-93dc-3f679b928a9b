package com.hospital.system.service;

import com.hospital.system.dto.DoctorDTO;
import com.hospital.system.model.Doctor;
import com.hospital.system.model.User;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

public interface DoctorService {
    DoctorDTO getDoctorById(Long id);
    
    DoctorDTO getDoctorByUserId(Long userId);
    
    DoctorDTO getCurrentDoctor();
    
    Doctor<PERSON><PERSON> createDoctor(DoctorDTO doctorDTO, User user);
    
    DoctorDTO updateDoctor(Long id, DoctorDTO doctorDTO);
    
    List<DoctorDTO> getAllDoctors();
    
    List<DoctorDTO> getDoctorsByDepartment(Long departmentId);
    
    List<DoctorDTO> getTopRatedDoctors(int limit);
    
    DoctorDTO updateDoctorRating(Long id, Integer rating);
    
    void deleteDoctor(Long id);
    
    Doctor getDoctorEntityById(Long id);
    
    Doctor getDoctorEntityByUser(User user);
    
    boolean existsByLicenseNumber(String licenseNumber);
    
    // 新增方法：上传医生资质证书
    String uploadCertificate(Long doctorId, MultipartFile file) throws IOException;
    
    // 新增方法：更新医生资质信息
    DoctorDTO updateQualification(Long doctorId, String resume, String educationBackground, String professionalTitle);
} 