package com.hospital.system.service.impl;

import com.hospital.system.dto.ScheduleDTO;
import com.hospital.system.model.Doctor;
import com.hospital.system.model.Schedule;
import com.hospital.system.model.User;
import com.hospital.system.model.UserRole;
import com.hospital.system.repository.DoctorRepository;
import com.hospital.system.repository.ScheduleRepository;
import com.hospital.system.service.DoctorService;
import com.hospital.system.service.ScheduleService;
import com.hospital.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ScheduleServiceImpl implements ScheduleService {

    @Autowired
    private ScheduleRepository scheduleRepository;
    
    @Autowired
    private DoctorRepository doctorRepository;
    
    @Autowired
    private DoctorService doctorService;
    
    @Autowired
    private UserService userService;

    @Override
    public ScheduleDTO getScheduleById(Long id) {
        Schedule schedule = getScheduleEntityById(id);
        return convertToDto(schedule);
    }

    @Override
    public List<ScheduleDTO> getAllSchedules() {
        return scheduleRepository.findAll().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<ScheduleDTO> getSchedulesByDoctor(Long doctorId) {
        return scheduleRepository.findByDoctorId(doctorId).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<ScheduleDTO> getSchedulesByDoctorAndDay(Long doctorId, DayOfWeek dayOfWeek) {
        return scheduleRepository.findByDoctorIdAndDayOfWeek(doctorId, dayOfWeek).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<ScheduleDTO> getSchedulesByDepartment(Long departmentId) {
        return scheduleRepository.findByDepartmentId(departmentId).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<ScheduleDTO> getAvailableSchedules(Long doctorId, LocalDate date) {
        DayOfWeek dayOfWeek = date.getDayOfWeek();
        return scheduleRepository.findAvailableSchedules(doctorId, dayOfWeek).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public ScheduleDTO createSchedule(ScheduleDTO scheduleDTO) {
        // 检查权限：医生只能创建自己的排班
        checkDoctorPermission(scheduleDTO.getDoctorId());
        
        Schedule schedule = new Schedule();
        updateScheduleFromDto(schedule, scheduleDTO);
        Schedule savedSchedule = scheduleRepository.save(schedule);
        return convertToDto(savedSchedule);
    }

    @Override
    public List<ScheduleDTO> createBatchSchedules(List<ScheduleDTO> schedules) {
        if (schedules.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 检查权限：医生只能创建自己的排班
        Long doctorId = schedules.get(0).getDoctorId();
        checkDoctorPermission(doctorId);
        
        // 确保所有排班都是同一个医生的
        for (ScheduleDTO dto : schedules) {
            if (!doctorId.equals(dto.getDoctorId())) {
                throw new RuntimeException("批量排班必须属于同一个医生");
            }
        }
        
        List<Schedule> scheduleEntities = new ArrayList<>();
        
        for (ScheduleDTO dto : schedules) {
            Schedule schedule = new Schedule();
            updateScheduleFromDto(schedule, dto);
            scheduleEntities.add(schedule);
        }
        
        List<Schedule> savedSchedules = scheduleRepository.saveAll(scheduleEntities);
        return savedSchedules.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public ScheduleDTO updateSchedule(Long id, ScheduleDTO scheduleDTO) {
        Schedule schedule = getScheduleEntityById(id);
        
        // 检查权限：医生只能更新自己的排班
        checkDoctorPermission(schedule.getDoctor().getId());
        
        updateScheduleFromDto(schedule, scheduleDTO);
        Schedule updatedSchedule = scheduleRepository.save(schedule);
        return convertToDto(updatedSchedule);
    }

    @Override
    public void deleteSchedule(Long id) {
        Schedule schedule = getScheduleEntityById(id);
        
        // 检查权限：医生只能删除自己的排班
        checkDoctorPermission(schedule.getDoctor().getId());
        
        // 检查排班是否有预约
        if (!schedule.getAppointments().isEmpty()) {
            throw new RuntimeException("无法删除已有预约的排班");
        }
        
        scheduleRepository.delete(schedule);
    }

    @Override
    public Schedule getScheduleEntityById(Long id) {
        return scheduleRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("排班不存在，ID: " + id));
    }
    
    private void checkDoctorPermission(Long doctorId) {
        User currentUser = userService.getCurrentUser();
        
        // 管理员可以管理所有排班
        if (currentUser.getRole() == UserRole.ADMIN) {
            return;
        }
        
        // 医生只能管理自己的排班
        if (currentUser.getRole() == UserRole.DOCTOR) {
            try {
                Doctor currentDoctor = doctorService.getDoctorEntityByUser(currentUser);
                if (!currentDoctor.getId().equals(doctorId)) {
                    throw new RuntimeException("您只能管理自己的排班");
                }
            } catch (Exception e) {
                throw new RuntimeException("无法验证医生身份，请联系管理员");
            }
        } else {
            throw new RuntimeException("只有医生和管理员可以管理排班");
        }
    }
    
    private ScheduleDTO convertToDto(Schedule schedule) {
        ScheduleDTO dto = new ScheduleDTO();
        dto.setId(schedule.getId());
        dto.setDoctorId(schedule.getDoctor().getId());
        dto.setDoctorName(schedule.getDoctor().getUser().getFullName());
        dto.setDepartmentId(schedule.getDoctor().getDepartment().getId());
        dto.setDepartmentName(schedule.getDoctor().getDepartment().getName());
        dto.setDayOfWeek(schedule.getDayOfWeek());
        dto.setStartTime(schedule.getStartTime());
        dto.setEndTime(schedule.getEndTime());
        dto.setAppointmentDuration(schedule.getAppointmentDuration());
        dto.setMaxAppointments(schedule.getMaxAppointments());
        dto.setBookedAppointments(schedule.getAppointments().size());
        dto.setAvailableSlots(schedule.getMaxAppointments() - schedule.getAppointments().size());
        return dto;
    }
    
    private void updateScheduleFromDto(Schedule schedule, ScheduleDTO dto) {
        if (dto.getDoctorId() != null) {
            Doctor doctor = doctorService.getDoctorEntityById(dto.getDoctorId());
            schedule.setDoctor(doctor);
        }
        schedule.setDayOfWeek(dto.getDayOfWeek());
        schedule.setStartTime(dto.getStartTime());
        schedule.setEndTime(dto.getEndTime());
        schedule.setAppointmentDuration(dto.getAppointmentDuration());
        schedule.setMaxAppointments(dto.getMaxAppointments());
    }
} 