package com.hospital.system.service.impl;

import com.hospital.system.dto.StatsResponse;
import com.hospital.system.model.Appointment;
import com.hospital.system.model.AppointmentStatus;
import com.hospital.system.repository.*;
import com.hospital.system.service.StatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.Month;
import java.time.format.TextStyle;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class StatisticsServiceImpl implements StatisticsService {

    @Autowired
    private PatientRepository patientRepository;
    
    @Autowired
    private DoctorRepository doctorRepository;
    
    @Autowired
    private DepartmentRepository departmentRepository;
    
    @Autowired
    private AppointmentRepository appointmentRepository;
    
    @Autowired
    private MedicalReportRepository medicalReportRepository;

    @Override
    public StatsResponse getSystemStatistics() {
        LocalDate today = LocalDate.now();
        
        StatsResponse response = new StatsResponse();
        response.setTotalPatients((int) patientRepository.count());
        response.setTotalDoctors((int) doctorRepository.count());
        response.setTotalAppointments((int) appointmentRepository.count());
        response.setTotalDepartments((int) departmentRepository.count());
        
        // 今日预约数
        List<Appointment> todayAppointments = appointmentRepository.findAppointmentsByDate(today);
        response.setTodayAppointments(todayAppointments.size());
        
        // 已完成和已取消的预约数
        long completedCount = appointmentRepository.findAll().stream()
                .filter(a -> a.getStatus() == AppointmentStatus.COMPLETED)
                .count();
        long cancelledCount = appointmentRepository.findAll().stream()
                .filter(a -> a.getStatus() == AppointmentStatus.CANCELLED)
                .count();
        response.setCompletedAppointments((int) completedCount);
        response.setCancelledAppointments((int) cancelledCount);
        
        // 获取科室统计数据
        LocalDate startDate = today.minusMonths(1);
        LocalDate endDate = today;
        response.setDepartmentStats(getDepartmentPopularityStatistics(startDate, endDate).getDepartmentStats());
        
        // 获取医生统计数据
        response.setDoctorStats(getDoctorWorkloadStatistics(startDate, endDate).getDoctorStats());
        
        // 获取月度预约统计
        response.setMonthlyAppointments(getMonthlyAppointmentStatistics(today.getYear()));
        
        return response;
    }

    @Override
    public StatsResponse getDoctorWorkloadStatistics(LocalDate startDate, LocalDate endDate) {
        List<Object[]> doctorStats = appointmentRepository.findDoctorWorkloadStatistics(startDate, endDate);
        
        List<StatsResponse.DoctorStatsDTO> doctorStatsList = new ArrayList<>();
        for (Object[] stat : doctorStats) {
            Long doctorId = (Long) stat[0];
            Long appointmentCount = (Long) stat[1];
            
            // 获取医生信息
            doctorRepository.findById(doctorId).ifPresent(doctor -> {
                StatsResponse.DoctorStatsDTO dto = new StatsResponse.DoctorStatsDTO();
                dto.setDoctorId(doctorId);
                dto.setDoctorName(doctor.getUser().getFullName());
                dto.setDepartmentName(doctor.getDepartment().getName());
                dto.setAppointmentCount(appointmentCount.intValue());
                dto.setRatingScore(doctor.getRatingScore());
                dto.setRatingCount(doctor.getRatingCount());
                doctorStatsList.add(dto);
            });
        }
        
        StatsResponse response = new StatsResponse();
        response.setDoctorStats(doctorStatsList);
        return response;
    }

    @Override
    public StatsResponse getDepartmentPopularityStatistics(LocalDate startDate, LocalDate endDate) {
        List<Object[]> departmentStats = appointmentRepository.findDepartmentPopularityStatistics(startDate, endDate);
        
        List<StatsResponse.DepartmentStatsDTO> departmentStatsList = new ArrayList<>();
        for (Object[] stat : departmentStats) {
            Long departmentId = (Long) stat[0];
            Long appointmentCount = (Long) stat[1];
            
            // 获取科室信息
            departmentRepository.findById(departmentId).ifPresent(department -> {
                StatsResponse.DepartmentStatsDTO dto = new StatsResponse.DepartmentStatsDTO();
                dto.setDepartmentId(departmentId);
                dto.setDepartmentName(department.getName());
                dto.setAppointmentCount(appointmentCount.intValue());
                dto.setDoctorCount(department.getDoctors().size());
                
                // 计算趋势百分比（这里简单模拟一个随机趋势）
                double trendPercentage = Math.random() * 20 - 10; // -10% 到 +10% 之间的随机值
                dto.setTrendPercentage(trendPercentage);
                
                departmentStatsList.add(dto);
            });
        }
        
        StatsResponse response = new StatsResponse();
        response.setDepartmentStats(departmentStatsList);
        return response;
    }

    @Override
    public StatsResponse getDoctorStatistics(Long doctorId) {
        LocalDate today = LocalDate.now();
        LocalDate startDate = today.minusMonths(1);
        
        StatsResponse response = new StatsResponse();
        
        // 获取医生信息
        doctorRepository.findById(doctorId).ifPresent(doctor -> {
            // 获取医生的预约数
            List<Appointment> appointments = appointmentRepository.findByDoctorId(doctorId);
            
            // 统计已完成和已取消的预约数
            long completedCount = appointments.stream()
                    .filter(a -> a.getStatus() == AppointmentStatus.COMPLETED)
                    .count();
            long cancelledCount = appointments.stream()
                    .filter(a -> a.getStatus() == AppointmentStatus.CANCELLED)
                    .count();
            
            // 获取今日预约数
            long todayCount = appointments.stream()
                    .filter(a -> a.getAppointmentDate().equals(today))
                    .count();
            
            response.setTotalAppointments(appointments.size());
            response.setCompletedAppointments((int) completedCount);
            response.setCancelledAppointments((int) cancelledCount);
            response.setTodayAppointments((int) todayCount);
            
            // 创建医生统计DTO
            StatsResponse.DoctorStatsDTO doctorStat = new StatsResponse.DoctorStatsDTO();
            doctorStat.setDoctorId(doctorId);
            doctorStat.setDoctorName(doctor.getUser().getFullName());
            doctorStat.setDepartmentName(doctor.getDepartment().getName());
            doctorStat.setAppointmentCount(appointments.size());
            doctorStat.setRatingScore(doctor.getRatingScore());
            doctorStat.setRatingCount(doctor.getRatingCount());
            
            response.setDoctorStats(Collections.singletonList(doctorStat));
            
            // 获取月度预约统计
            response.setMonthlyAppointments(getMonthlyAppointmentStatisticsByDoctor(doctorId, today.getYear()));
        });
        
        return response;
    }

    @Override
    public StatsResponse getDepartmentStatistics(Long departmentId) {
        LocalDate today = LocalDate.now();
        
        StatsResponse response = new StatsResponse();
        
        // 获取科室信息
        departmentRepository.findById(departmentId).ifPresent(department -> {
            // 获取科室的预约数
            List<Appointment> appointments = appointmentRepository.findByDepartmentId(departmentId);
            
            // 统计已完成和已取消的预约数
            long completedCount = appointments.stream()
                    .filter(a -> a.getStatus() == AppointmentStatus.COMPLETED)
                    .count();
            long cancelledCount = appointments.stream()
                    .filter(a -> a.getStatus() == AppointmentStatus.CANCELLED)
                    .count();
            
            // 获取今日预约数
            long todayCount = appointments.stream()
                    .filter(a -> a.getAppointmentDate().equals(today))
                    .count();
            
            response.setTotalDoctors(department.getDoctors().size());
            response.setTotalAppointments(appointments.size());
            response.setCompletedAppointments((int) completedCount);
            response.setCancelledAppointments((int) cancelledCount);
            response.setTodayAppointments((int) todayCount);
            
            // 创建科室统计DTO
            StatsResponse.DepartmentStatsDTO departmentStat = new StatsResponse.DepartmentStatsDTO();
            departmentStat.setDepartmentId(departmentId);
            departmentStat.setDepartmentName(department.getName());
            departmentStat.setAppointmentCount(appointments.size());
            departmentStat.setDoctorCount(department.getDoctors().size());
            
            // 计算趋势百分比（这里简单模拟一个随机趋势）
            double trendPercentage = Math.random() * 20 - 10; // -10% 到 +10% 之间的随机值
            departmentStat.setTrendPercentage(trendPercentage);
            
            response.setDepartmentStats(Collections.singletonList(departmentStat));
            
            // 获取月度预约统计
            response.setMonthlyAppointments(getMonthlyAppointmentStatisticsByDepartment(departmentId, today.getYear()));
        });
        
        return response;
    }

    @Override
    public Map<String, Integer> getMonthlyAppointmentStatistics(int year) {
        Map<String, Integer> monthlyStats = new LinkedHashMap<>();
        
        // 初始化每个月的统计数据
        for (Month month : Month.values()) {
            monthlyStats.put(month.getDisplayName(TextStyle.FULL, Locale.CHINA), 0);
        }
        
        // 获取所有预约
        List<Appointment> appointments = appointmentRepository.findAll();
        
        // 按月统计预约数
        for (Appointment appointment : appointments) {
            if (appointment.getAppointmentDate().getYear() == year) {
                Month month = appointment.getAppointmentDate().getMonth();
                String monthName = month.getDisplayName(TextStyle.FULL, Locale.CHINA);
                monthlyStats.put(monthName, monthlyStats.get(monthName) + 1);
            }
        }
        
        return monthlyStats;
    }

    @Override
    public Map<String, Integer> getMonthlyAppointmentStatisticsByDoctor(Long doctorId, int year) {
        Map<String, Integer> monthlyStats = new LinkedHashMap<>();
        
        // 初始化每个月的统计数据
        for (Month month : Month.values()) {
            monthlyStats.put(month.getDisplayName(TextStyle.FULL, Locale.CHINA), 0);
        }
        
        // 获取医生的所有预约
        List<Appointment> appointments = appointmentRepository.findByDoctorId(doctorId);
        
        // 按月统计预约数
        for (Appointment appointment : appointments) {
            if (appointment.getAppointmentDate().getYear() == year) {
                Month month = appointment.getAppointmentDate().getMonth();
                String monthName = month.getDisplayName(TextStyle.FULL, Locale.CHINA);
                monthlyStats.put(monthName, monthlyStats.get(monthName) + 1);
            }
        }
        
        return monthlyStats;
    }

    @Override
    public Map<String, Integer> getMonthlyAppointmentStatisticsByDepartment(Long departmentId, int year) {
        Map<String, Integer> monthlyStats = new LinkedHashMap<>();
        
        // 初始化每个月的统计数据
        for (Month month : Month.values()) {
            monthlyStats.put(month.getDisplayName(TextStyle.FULL, Locale.CHINA), 0);
        }
        
        // 获取科室的所有预约
        List<Appointment> appointments = appointmentRepository.findByDepartmentId(departmentId);
        
        // 按月统计预约数
        for (Appointment appointment : appointments) {
            if (appointment.getAppointmentDate().getYear() == year) {
                Month month = appointment.getAppointmentDate().getMonth();
                String monthName = month.getDisplayName(TextStyle.FULL, Locale.CHINA);
                monthlyStats.put(monthName, monthlyStats.get(monthName) + 1);
            }
        }
        
        return monthlyStats;
    }
} 