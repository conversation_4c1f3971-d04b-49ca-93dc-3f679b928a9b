package com.hospital.system.service.impl;

import com.hospital.system.dto.PatientDTO;
import com.hospital.system.model.Patient;
import com.hospital.system.model.User;
import com.hospital.system.repository.PatientRepository;
import com.hospital.system.service.PatientService;
import com.hospital.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class PatientServiceImpl implements PatientService {

    private static final Logger logger = LoggerFactory.getLogger(PatientServiceImpl.class);

    @Autowired
    private PatientRepository patientRepository;
    
    @Autowired
    private UserService userService;

    @Override
    public PatientDTO getPatientById(Long id) {
        Patient patient = getPatientEntityById(id);
        return convertToDto(patient);
    }

    @Override
    public PatientDTO getPatientByUserId(Long userId) {
        Patient patient = patientRepository.findAll().stream()
                .filter(p -> p.getUser().getId().equals(userId))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("Patient not found with user id: " + userId));
        return convertToDto(patient);
    }

    @Override
    public PatientDTO getCurrentPatient() {
        User currentUser = userService.getCurrentUser();
        Patient patient = getPatientEntityByUser(currentUser);
        return convertToDto(patient);
    }

    @Override
    public PatientDTO createPatient(PatientDTO patientDTO, User user) {
        Patient patient = new Patient();
        patient.setUser(user);
        updatePatientFromDto(patient, patientDTO);
        Patient savedPatient = patientRepository.save(patient);
        return convertToDto(savedPatient);
    }

    @Override
    public PatientDTO updatePatient(Long id, PatientDTO patientDTO) {
        Patient patient = getPatientEntityById(id);
        updatePatientFromDto(patient, patientDTO);
        Patient updatedPatient = patientRepository.save(patient);
        return convertToDto(updatedPatient);
    }

    @Override
    public List<PatientDTO> getAllPatients() {
        return patientRepository.findAll().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public void deletePatient(Long id) {
        Patient patient = getPatientEntityById(id);
        patientRepository.delete(patient);
    }

    @Override
    public Patient getPatientEntityById(Long id) {
        return patientRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Patient not found with id: " + id));
    }

    @Override
    public Patient getPatientEntityByUser(User user) {
        if (user == null) {
            logger.error("尝试获取患者信息时用户为空");
            throw new RuntimeException("无法获取患者信息：用户未登录");
        }
        
        logger.debug("正在查找用户ID为 {} 的患者信息", user.getId());
        return patientRepository.findByUser(user)
                .orElseGet(() -> {
                    // 如果患者记录不存在，自动创建一个基本的患者记录
                    logger.info("用户ID为 {} 的患者信息未找到，正在自动创建基本患者记录", user.getId());
                    Patient newPatient = new Patient();
                    newPatient.setUser(user);
                    newPatient.setGender("未知"); // 设置默认值
                    // 其他字段保持默认值，可以之后由用户更新
                    return patientRepository.save(newPatient);
                });
    }
    
    private PatientDTO convertToDto(Patient patient) {
        PatientDTO dto = new PatientDTO();
        dto.setId(patient.getId());
        dto.setUserId(patient.getUser().getId());
        dto.setMedicalHistory(patient.getMedicalHistory());
        dto.setBirthDate(patient.getBirthDate());
        dto.setGender(patient.getGender());
        dto.setAddress(patient.getAddress());
        dto.setEmergencyContact(patient.getEmergencyContact());
        dto.setFullName(patient.getUser().getFullName());
        dto.setPhoneNumber(patient.getUser().getPhoneNumber());
        dto.setEmail(patient.getUser().getEmail());
        return dto;
    }
    
    private void updatePatientFromDto(Patient patient, PatientDTO dto) {
        patient.setMedicalHistory(dto.getMedicalHistory());
        patient.setBirthDate(dto.getBirthDate());
        patient.setGender(dto.getGender());
        patient.setAddress(dto.getAddress());
        patient.setEmergencyContact(dto.getEmergencyContact());
    }
} 