package com.hospital.system.service.impl;

import com.hospital.system.dto.DoctorDTO;
import com.hospital.system.model.Department;
import com.hospital.system.model.Doctor;
import com.hospital.system.model.User;
import com.hospital.system.repository.DepartmentRepository;
import com.hospital.system.repository.DoctorRepository;
import com.hospital.system.repository.UserRepository;
import com.hospital.system.service.DoctorService;
import com.hospital.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
public class DoctorServiceImpl implements DoctorService {

    @Value("${file.upload-dir:./uploads}")
    private String uploadDir;

    @Autowired
    private DoctorRepository doctorRepository;
    
    @Autowired
    private DepartmentRepository departmentRepository;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private UserRepository userRepository;

    @Override
    public DoctorDTO getDoctorById(Long id) {
        Doctor doctor = getDoctorEntityById(id);
        return convertToDto(doctor);
    }

    @Override
    public DoctorDTO getDoctorByUserId(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with id: " + userId));
        Doctor doctor = doctorRepository.findByUser(user)
                .orElseThrow(() -> new RuntimeException("Doctor not found with user id: " + userId));
        return convertToDto(doctor);
    }

    @Override
    public DoctorDTO getCurrentDoctor() {
        User currentUser = userService.getCurrentUser();
        Doctor doctor = getDoctorEntityByUser(currentUser);
        return convertToDto(doctor);
    }

    @Override
    public List<DoctorDTO> getAllDoctors() {
        return doctorRepository.findAll().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<DoctorDTO> getDoctorsByDepartment(Long departmentId) {
        Department department = departmentRepository.findById(departmentId)
                .orElseThrow(() -> new RuntimeException("Department not found with id: " + departmentId));
        return doctorRepository.findByDepartment(department).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<DoctorDTO> getTopRatedDoctors(int limit) {
        return doctorRepository.findTopRatedDoctors().stream()
                .limit(limit)
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }
    
    @Override
    public DoctorDTO updateDoctorRating(Long id, Integer rating) {
        Doctor doctor = getDoctorEntityById(id);
        
        // 更新医生评分
        if (doctor.getRatingCount() == null) {
            doctor.setRatingCount(1);
            doctor.setRatingScore((double) rating);
        } else {
            double currentTotal = doctor.getRatingScore() * doctor.getRatingCount();
            doctor.setRatingCount(doctor.getRatingCount() + 1);
            doctor.setRatingScore((currentTotal + rating) / doctor.getRatingCount());
        }
        
        Doctor updatedDoctor = doctorRepository.save(doctor);
        return convertToDto(updatedDoctor);
    }
    
    @Override
    public boolean existsByLicenseNumber(String licenseNumber) {
        return doctorRepository.existsByLicenseNumber(licenseNumber);
    }

    @Override
    public DoctorDTO createDoctor(DoctorDTO doctorDTO, User user) {
        Doctor doctor = new Doctor();
        doctor.setUser(user);
        updateDoctorFromDto(doctor, doctorDTO);
        Doctor savedDoctor = doctorRepository.save(doctor);
        return convertToDto(savedDoctor);
    }

    @Override
    public DoctorDTO updateDoctor(Long id, DoctorDTO doctorDTO) {
        Doctor doctor = getDoctorEntityById(id);
        updateDoctorFromDto(doctor, doctorDTO);
        Doctor updatedDoctor = doctorRepository.save(doctor);
        return convertToDto(updatedDoctor);
    }

    @Override
    public void deleteDoctor(Long id) {
        Doctor doctor = getDoctorEntityById(id);
        doctorRepository.delete(doctor);
    }

    @Override
    public Doctor getDoctorEntityById(Long id) {
        return doctorRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Doctor not found with id: " + id));
    }

    @Override
    public Doctor getDoctorEntityByUser(User user) {
        return doctorRepository.findByUser(user)
                .orElseThrow(() -> new RuntimeException("医生信息不存在，请联系管理员完善医生资料"));
    }
    
    @Override
    public String uploadCertificate(Long doctorId, MultipartFile file) throws IOException {
        Doctor doctor = getDoctorEntityById(doctorId);
        
        // 创建上传目录
        File directory = new File(uploadDir + "/certificates");
        if (!directory.exists()) {
            directory.mkdirs();
        }
        
        // 生成唯一文件名
        String originalFileName = file.getOriginalFilename();
        String fileExtension = originalFileName.substring(originalFileName.lastIndexOf("."));
        String fileName = UUID.randomUUID().toString() + fileExtension;
        String filePath = uploadDir + "/certificates/" + fileName;
        
        // 保存文件
        Path path = Paths.get(filePath);
        Files.write(path, file.getBytes());
        
        // 更新医生的证书路径
        doctor.setCertificatePath(filePath);
        doctorRepository.save(doctor);
        
        return filePath;
    }
    
    @Override
    public DoctorDTO updateQualification(Long doctorId, String resume, String educationBackground, String professionalTitle) {
        Doctor doctor = getDoctorEntityById(doctorId);
        
        if (resume != null) {
            doctor.setResume(resume);
        }
        
        if (educationBackground != null) {
            doctor.setEducationBackground(educationBackground);
        }
        
        if (professionalTitle != null) {
            doctor.setProfessionalTitle(professionalTitle);
        }
        
        Doctor updatedDoctor = doctorRepository.save(doctor);
        return convertToDto(updatedDoctor);
    }
    
    private DoctorDTO convertToDto(Doctor doctor) {
        DoctorDTO dto = new DoctorDTO();
        dto.setId(doctor.getId());
        dto.setUserId(doctor.getUser().getId());
        dto.setDepartmentId(doctor.getDepartment().getId());
        dto.setDepartmentName(doctor.getDepartment().getName());
        dto.setSpecialization(doctor.getSpecialization());
        dto.setLicenseNumber(doctor.getLicenseNumber());
        dto.setQualification(doctor.getQualification());
        dto.setYearsOfExperience(doctor.getYearsOfExperience());
        dto.setRatingScore(doctor.getRatingScore());
        dto.setRatingCount(doctor.getRatingCount());
        dto.setFullName(doctor.getUser().getFullName());
        dto.setPhoneNumber(doctor.getUser().getPhoneNumber());
        dto.setEmail(doctor.getUser().getEmail());
        
        // 添加资质信息字段
        dto.setCertificatePath(doctor.getCertificatePath());
        dto.setResume(doctor.getResume());
        dto.setEducationBackground(doctor.getEducationBackground());
        dto.setProfessionalTitle(doctor.getProfessionalTitle());
        
        return dto;
    }
    
    private void updateDoctorFromDto(Doctor doctor, DoctorDTO dto) {
        if (dto.getDepartmentId() != null) {
            Department department = departmentRepository.findById(dto.getDepartmentId())
                    .orElseThrow(() -> new RuntimeException("Department not found with id: " + dto.getDepartmentId()));
            doctor.setDepartment(department);
        }
        doctor.setSpecialization(dto.getSpecialization());
        doctor.setLicenseNumber(dto.getLicenseNumber());
        doctor.setQualification(dto.getQualification());
        doctor.setYearsOfExperience(dto.getYearsOfExperience());
    }
} 