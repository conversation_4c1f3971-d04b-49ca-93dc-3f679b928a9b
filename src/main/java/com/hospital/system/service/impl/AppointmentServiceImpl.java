package com.hospital.system.service.impl;

import com.hospital.system.dto.AppointmentDTO;
import com.hospital.system.dto.AppointmentRequest;
import com.hospital.system.dto.DiagnosisRequest;
import com.hospital.system.model.*;
import com.hospital.system.repository.AppointmentRepository;
import com.hospital.system.repository.MedicalReportRepository;
import com.hospital.system.repository.ScheduleRepository;
import com.hospital.system.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
public class AppointmentServiceImpl implements AppointmentService {

    private static final Logger logger = LoggerFactory.getLogger(AppointmentServiceImpl.class);

    @Autowired
    private AppointmentRepository appointmentRepository;
    
    @Autowired
    private ScheduleRepository scheduleRepository;
    
    @Autowired
    private MedicalReportRepository medicalReportRepository;
    
    @Autowired
    private PatientService patientService;
    
    @Autowired
    private DoctorService doctorService;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private ScheduleService scheduleService;

    @Override
    public AppointmentDTO getAppointmentById(Long id) {
        Appointment appointment = getAppointmentEntityById(id);
        return convertToDto(appointment);
    }

    @Override
    public AppointmentDTO getAppointmentByNumber(String appointmentNumber) {
        Appointment appointment = appointmentRepository.findByAppointmentNumber(appointmentNumber)
                .orElseThrow(() -> new RuntimeException("Appointment not found with number: " + appointmentNumber));
        return convertToDto(appointment);
    }

    @Override
    public List<AppointmentDTO> getAllAppointments() {
        return appointmentRepository.findAll().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<AppointmentDTO> getAppointmentsByPatient(Long patientId) {
        return appointmentRepository.findByPatientId(patientId).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<AppointmentDTO> getCurrentPatientAppointments() {
        Patient patient = patientService.getPatientEntityByUser(userService.getCurrentUser());
        return appointmentRepository.findByPatientId(patient.getId()).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<AppointmentDTO> getAppointmentsByDoctor(Long doctorId) {
        return appointmentRepository.findByDoctorId(doctorId).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<AppointmentDTO> getCurrentDoctorAppointments() {
        try {
            User currentUser = userService.getCurrentUser();
            if (currentUser.getRole() != UserRole.DOCTOR) {
                logger.warn("非医生角色用户尝试访问医生预约列表: userId={}", currentUser.getId());
                return new ArrayList<>();
            }
            
            Doctor doctor;
            try {
                doctor = doctorService.getDoctorEntityByUser(currentUser);
            } catch (Exception e) {
                logger.warn("医生信息不存在，可能是新注册的医生账号尚未完善信息: userId={}", currentUser.getId());
                return new ArrayList<>();
            }
            
            return appointmentRepository.findByDoctorId(doctor.getId()).stream()
                    .map(this::convertToDto)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("获取医生预约列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<AppointmentDTO> getAppointmentsByDoctorAndDate(Long doctorId, LocalDate date) {
        return appointmentRepository.findByDoctorIdAndAppointmentDate(doctorId, date).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<AppointmentDTO> getTodayAppointmentsForCurrentDoctor() {
        try {
            User currentUser = userService.getCurrentUser();
            if (currentUser.getRole() != UserRole.DOCTOR) {
                logger.warn("非医生角色用户尝试访问医生预约列表: userId={}", currentUser.getId());
                return new ArrayList<>();
            }
            
            Doctor doctor;
            try {
                doctor = doctorService.getDoctorEntityByUser(currentUser);
            } catch (Exception e) {
                logger.warn("医生信息不存在，可能是新注册的医生账号尚未完善信息: userId={}", currentUser.getId());
                return new ArrayList<>();
            }
            
            LocalDate today = LocalDate.now();
            return appointmentRepository.findByDoctorIdAndAppointmentDate(doctor.getId(), today).stream()
                    .map(this::convertToDto)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("获取今日预约列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<AppointmentDTO> getAppointmentsByDepartment(Long departmentId) {
        return appointmentRepository.findByDepartmentId(departmentId).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<AppointmentDTO> getAppointmentsByStatus(AppointmentStatus status) {
        return appointmentRepository.findAll().stream()
                .filter(appointment -> appointment.getStatus() == status)
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<AppointmentDTO> getAppointmentsByDateRange(LocalDate startDate, LocalDate endDate) {
        return appointmentRepository.findAll().stream()
                .filter(appointment -> 
                    !appointment.getAppointmentDate().isBefore(startDate) && 
                    !appointment.getAppointmentDate().isAfter(endDate))
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public AppointmentDTO bookAppointment(AppointmentRequest appointmentRequest) {
        try {
            logger.debug("开始预约流程，请求数据: {}", appointmentRequest);
            
            // 获取当前登录的患者
            User currentUser = userService.getCurrentUser();
            logger.debug("当前用户ID: {}", currentUser.getId());
            
            Patient patient;
            try {
                patient = patientService.getPatientEntityByUser(currentUser);
                logger.debug("成功获取患者信息，患者ID: {}", patient.getId());
            } catch (Exception e) {
                logger.error("获取患者信息失败: {}", e.getMessage());
                throw new RuntimeException("无法完成预约：" + e.getMessage(), e);
            }
            
            // 获取医生
            Doctor doctor;
            try {
                doctor = doctorService.getDoctorEntityById(appointmentRequest.getDoctorId());
                logger.debug("成功获取医生信息，医生ID: {}, 姓名: {}", doctor.getId(), doctor.getUser().getFullName());
            } catch (Exception e) {
                logger.error("获取医生信息失败: {}", e.getMessage());
                throw new RuntimeException("找不到对应的医生信息", e);
            }
            
            // 检查时间段是否可用
            if (!isTimeSlotAvailable(doctor.getId(), appointmentRequest.getAppointmentDate(), 
                    appointmentRequest.getStartTime(), appointmentRequest.getEndTime())) {
                logger.warn("所选时间段不可用：doctorId={}, date={}, startTime={}, endTime={}",
                        doctor.getId(), appointmentRequest.getAppointmentDate(),
                        appointmentRequest.getStartTime(), appointmentRequest.getEndTime());
                throw new RuntimeException("所选时间段已被预约，请选择其他时间");
            }
            
            // 创建预约
            Appointment appointment = new Appointment();
            appointment.setPatient(patient);
            appointment.setDoctor(doctor);
            appointment.setAppointmentDate(appointmentRequest.getAppointmentDate());
            appointment.setStartTime(appointmentRequest.getStartTime());
            appointment.setEndTime(appointmentRequest.getEndTime());
            appointment.setSymptoms(appointmentRequest.getSymptoms());
            appointment.setStatus(AppointmentStatus.SCHEDULED);
            appointment.setAppointmentNumber(generateAppointmentNumber());
            
            // 如果提供了排班ID，则关联排班
            if (appointmentRequest.getScheduleId() != null) {
                try {
                    Schedule schedule = scheduleService.getScheduleEntityById(appointmentRequest.getScheduleId());
                    appointment.setSchedule(schedule);
                    logger.debug("关联排班信息，排班ID: {}", schedule.getId());
                } catch (Exception e) {
                    logger.error("获取排班信息失败: {}", e.getMessage());
                    throw new RuntimeException("找不到对应的排班信息", e);
                }
            }
            
            Appointment savedAppointment = appointmentRepository.save(appointment);
            logger.info("预约成功创建，ID: {}, 预约号: {}", savedAppointment.getId(), savedAppointment.getAppointmentNumber());
            return convertToDto(savedAppointment);
        } catch (Exception e) {
            logger.error("预约创建过程中发生错误: {}", e.getMessage(), e);
            throw e; // 重新抛出异常以便控制器处理
        }
    }

    @Override
    @Transactional
    public AppointmentDTO updateAppointmentStatus(Long id, AppointmentStatus status) {
        Appointment appointment = getAppointmentEntityById(id);
        appointment.setStatus(status);
        Appointment updatedAppointment = appointmentRepository.save(appointment);
        return convertToDto(updatedAppointment);
    }

    @Override
    @Transactional
    public AppointmentDTO updateAppointment(Long id, AppointmentDTO appointmentDTO) {
        Appointment appointment = getAppointmentEntityById(id);
        
        // 如果更改了时间，需要检查新时间段是否可用
        if (!appointment.getAppointmentDate().equals(appointmentDTO.getAppointmentDate()) ||
            !appointment.getStartTime().equals(appointmentDTO.getStartTime()) ||
            !appointment.getEndTime().equals(appointmentDTO.getEndTime())) {
            
            if (!isTimeSlotAvailable(appointmentDTO.getDoctorId(), appointmentDTO.getAppointmentDate(), 
                    appointmentDTO.getStartTime(), appointmentDTO.getEndTime())) {
                throw new RuntimeException("Selected time slot is not available");
            }
        }
        
        appointment.setAppointmentDate(appointmentDTO.getAppointmentDate());
        appointment.setStartTime(appointmentDTO.getStartTime());
        appointment.setEndTime(appointmentDTO.getEndTime());
        appointment.setSymptoms(appointmentDTO.getSymptoms());
        appointment.setStatus(appointmentDTO.getStatus());
        
        Appointment updatedAppointment = appointmentRepository.save(appointment);
        return convertToDto(updatedAppointment);
    }

    @Override
    @Transactional
    public AppointmentDTO submitDiagnosis(DiagnosisRequest diagnosisRequest) {
        Appointment appointment = getAppointmentEntityById(diagnosisRequest.getAppointmentId());
        
        // 更新诊断信息
        appointment.setDiagnosisNotes(diagnosisRequest.getDiagnosisNotes());
        appointment.setStatus(AppointmentStatus.COMPLETED);
        
        // 如果提供了报告信息，创建医疗报告
        if (diagnosisRequest.getReportType() != null && diagnosisRequest.getReportResults() != null) {
            createMedicalReport(appointment.getId(), diagnosisRequest.getReportType(), diagnosisRequest.getReportResults());
        }
        
        Appointment updatedAppointment = appointmentRepository.save(appointment);
        return convertToDto(updatedAppointment);
    }

    @Override
    @Transactional
    public MedicalReport createMedicalReport(Long appointmentId, String reportType, String results) {
        Appointment appointment = getAppointmentEntityById(appointmentId);
        
        MedicalReport report = new MedicalReport();
        report.setAppointment(appointment);
        report.setReportType(reportType);
        report.setResults(results);
        report.setReportDate(LocalDate.now());
        
        return medicalReportRepository.save(report);
    }

    @Override
    @Transactional
    public void cancelAppointment(Long id) {
        Appointment appointment = getAppointmentEntityById(id);
        appointment.setStatus(AppointmentStatus.CANCELLED);
        appointmentRepository.save(appointment);
    }

    @Override
    @Transactional
    public void deleteAppointment(Long id) {
        appointmentRepository.deleteById(id);
    }

    @Override
    public String generateAppointmentNumber() {
        return "APT-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }

    @Override
    public boolean isTimeSlotAvailable(Long doctorId, LocalDate date, LocalTime startTime, LocalTime endTime) {
        // 获取医生在指定日期的所有预约
        List<Appointment> doctorAppointments = appointmentRepository.findByDoctorIdAndAppointmentDate(doctorId, date);
        
        // 检查是否有时间冲突的预约
        for (Appointment existingAppointment : doctorAppointments) {
            // 跳过已取消的预约
            if (existingAppointment.getStatus() == AppointmentStatus.CANCELLED) {
                continue;
            }
            
            // 检查时间冲突
            if ((startTime.isBefore(existingAppointment.getEndTime()) || startTime.equals(existingAppointment.getEndTime())) && 
                (endTime.isAfter(existingAppointment.getStartTime()) || endTime.equals(existingAppointment.getStartTime()))) {
                return false;
            }
        }
        
        return true;
    }

    @Override
    public Appointment getAppointmentEntityById(Long id) {
        return appointmentRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Appointment not found with id: " + id));
    }
    
    private AppointmentDTO convertToDto(Appointment appointment) {
        AppointmentDTO dto = new AppointmentDTO();
        dto.setId(appointment.getId());
        dto.setPatientId(appointment.getPatient().getId());
        dto.setPatientName(appointment.getPatient().getUser().getFullName());
        dto.setDoctorId(appointment.getDoctor().getId());
        dto.setDoctorName(appointment.getDoctor().getUser().getFullName());
        
        if (appointment.getSchedule() != null) {
            dto.setScheduleId(appointment.getSchedule().getId());
        }
        
        dto.setDepartmentId(appointment.getDoctor().getDepartment().getId());
        dto.setDepartmentName(appointment.getDoctor().getDepartment().getName());
        dto.setAppointmentDate(appointment.getAppointmentDate());
        dto.setStartTime(appointment.getStartTime());
        dto.setEndTime(appointment.getEndTime());
        dto.setAppointmentNumber(appointment.getAppointmentNumber());
        dto.setStatus(appointment.getStatus());
        dto.setSymptoms(appointment.getSymptoms());
        dto.setDiagnosisNotes(appointment.getDiagnosisNotes());
        dto.setCreatedAt(appointment.getCreatedAt());
        
        return dto;
    }
} 