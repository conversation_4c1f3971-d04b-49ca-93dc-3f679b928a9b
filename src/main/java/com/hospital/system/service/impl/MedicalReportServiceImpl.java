package com.hospital.system.service.impl;

import com.hospital.system.dto.MedicalReportDTO;
import com.hospital.system.model.Appointment;
import com.hospital.system.model.Doctor;
import com.hospital.system.model.MedicalReport;
import com.hospital.system.model.Patient;
import com.hospital.system.model.User;
import com.hospital.system.repository.AppointmentRepository;
import com.hospital.system.repository.MedicalReportRepository;
import com.hospital.system.service.DoctorService;
import com.hospital.system.service.MedicalReportService;
import com.hospital.system.service.PatientService;
import com.hospital.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.Optional;

@Service
public class MedicalReportServiceImpl implements MedicalReportService {
    
    private static final Logger logger = LoggerFactory.getLogger(MedicalReportServiceImpl.class);

    @Autowired
    private MedicalReportRepository medicalReportRepository;
    
    @Autowired
    private AppointmentRepository appointmentRepository;
    
    @Autowired
    private PatientService patientService;
    
    @Autowired
    private DoctorService doctorService;
    
    @Autowired
    private UserService userService;

    @Override
    public MedicalReportDTO getMedicalReportById(Long id) {
        MedicalReport medicalReport = getMedicalReportEntityById(id);
        return convertToDto(medicalReport);
    }

    @Override
    public List<MedicalReportDTO> getAllMedicalReports() {
        return medicalReportRepository.findAll().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<MedicalReportDTO> getMedicalReportsByPatient(Long patientId) {
        return medicalReportRepository.findByPatientId(patientId).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<MedicalReportDTO> getMedicalReportsByCurrentPatient() {
        try {
            User currentUser = userService.getCurrentUser();
            Patient patient = patientService.getPatientEntityByUser(currentUser);
            return getMedicalReportsByPatient(patient.getId());
        } catch (Exception e) {
            return Collections.emptyList();
        }
    }

    @Override
    public List<MedicalReportDTO> getMedicalReportsByDoctor(Long doctorId) {
        return medicalReportRepository.findByDoctorId(doctorId).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<MedicalReportDTO> getMedicalReportsByAppointment(Long appointmentId) {
        return medicalReportRepository.findByAppointmentId(appointmentId).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<MedicalReportDTO> getMedicalReportsByDateRange(LocalDate startDate, LocalDate endDate) {
        return medicalReportRepository.findByReportDateBetween(startDate, endDate).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public MedicalReportDTO createMedicalReport(MedicalReportDTO medicalReportDTO) {
        Appointment appointment = appointmentRepository.findById(medicalReportDTO.getAppointmentId())
                .orElseThrow(() -> new RuntimeException("Appointment not found with id: " + medicalReportDTO.getAppointmentId()));
        
        MedicalReport medicalReport = new MedicalReport();
        medicalReport.setAppointment(appointment);
        updateMedicalReportFromDto(medicalReport, medicalReportDTO);
        
        MedicalReport savedMedicalReport = medicalReportRepository.save(medicalReport);
        return convertToDto(savedMedicalReport);
    }

    @Override
    public MedicalReportDTO updateMedicalReport(Long id, MedicalReportDTO medicalReportDTO) {
        MedicalReport medicalReport = getMedicalReportEntityById(id);
        updateMedicalReportFromDto(medicalReport, medicalReportDTO);
        
        MedicalReport updatedMedicalReport = medicalReportRepository.save(medicalReport);
        return convertToDto(updatedMedicalReport);
    }

    @Override
    public void deleteMedicalReport(Long id) {
        MedicalReport medicalReport = getMedicalReportEntityById(id);
        medicalReportRepository.delete(medicalReport);
    }

    @Override
    public MedicalReport getMedicalReportEntityById(Long id) {
        return medicalReportRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Medical report not found with id: " + id));
    }
    
    private MedicalReportDTO convertToDto(MedicalReport medicalReport) {
        MedicalReportDTO dto = new MedicalReportDTO();
        dto.setId(medicalReport.getId());
        dto.setAppointmentId(medicalReport.getAppointment().getId());
        dto.setPatientId(medicalReport.getAppointment().getPatient().getId());
        dto.setPatientName(medicalReport.getAppointment().getPatient().getUser().getFullName());
        dto.setDoctorId(medicalReport.getAppointment().getDoctor().getId());
        dto.setDoctorName(medicalReport.getAppointment().getDoctor().getUser().getFullName());
        dto.setReportType(medicalReport.getReportType());
        dto.setReportDate(medicalReport.getReportDate());
        dto.setResults(medicalReport.getResults());
        dto.setFilePath(medicalReport.getFilePath());
        return dto;
    }
    
    private void updateMedicalReportFromDto(MedicalReport medicalReport, MedicalReportDTO dto) {
        medicalReport.setReportType(dto.getReportType());
        medicalReport.setResults(dto.getResults());
        medicalReport.setFilePath(dto.getFilePath());
        
        // 如果没有设置报告日期，则使用当前日期
        if (medicalReport.getReportDate() == null) {
            medicalReport.setReportDate(LocalDate.now());
        }
    }
} 