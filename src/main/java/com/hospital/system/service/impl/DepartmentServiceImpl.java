package com.hospital.system.service.impl;

import com.hospital.system.dto.DepartmentDTO;
import com.hospital.system.model.Department;
import com.hospital.system.repository.DepartmentRepository;
import com.hospital.system.service.DepartmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class DepartmentServiceImpl implements DepartmentService {

    @Autowired
    private DepartmentRepository departmentRepository;

    @Override
    public DepartmentDTO getDepartmentById(Long id) {
        Department department = getDepartmentEntityById(id);
        return convertToDto(department);
    }

    @Override
    public List<DepartmentDTO> getAllDepartments() {
        return departmentRepository.findAll().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public DepartmentDTO createDepartment(DepartmentDTO departmentDTO) {
        if (existsByName(departmentDTO.getName())) {
            throw new RuntimeException("Department with name " + departmentDTO.getName() + " already exists");
        }
        
        Department department = new Department();
        updateDepartmentFromDto(department, departmentDTO);
        Department savedDepartment = departmentRepository.save(department);
        return convertToDto(savedDepartment);
    }

    @Override
    public DepartmentDTO updateDepartment(Long id, DepartmentDTO departmentDTO) {
        Department department = getDepartmentEntityById(id);
        
        // 检查名称是否已存在（如果更改了名称）
        if (!department.getName().equals(departmentDTO.getName()) && existsByName(departmentDTO.getName())) {
            throw new RuntimeException("Department with name " + departmentDTO.getName() + " already exists");
        }
        
        updateDepartmentFromDto(department, departmentDTO);
        Department updatedDepartment = departmentRepository.save(department);
        return convertToDto(updatedDepartment);
    }

    @Override
    public void deleteDepartment(Long id) {
        Department department = getDepartmentEntityById(id);
        
        // 检查科室是否有医生
        if (!department.getDoctors().isEmpty()) {
            throw new RuntimeException("Cannot delete department with associated doctors");
        }
        
        departmentRepository.delete(department);
    }

    @Override
    public Department getDepartmentEntityById(Long id) {
        return departmentRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Department not found with id: " + id));
    }

    @Override
    public boolean existsByName(String name) {
        return departmentRepository.existsByName(name);
    }
    
    private DepartmentDTO convertToDto(Department department) {
        DepartmentDTO dto = new DepartmentDTO();
        dto.setId(department.getId());
        dto.setName(department.getName());
        dto.setDescription(department.getDescription());
        dto.setFloorNumber(department.getFloorNumber());
        dto.setRoomNumber(department.getRoomNumber());
        dto.setDoctorCount(department.getDoctors().size());
        return dto;
    }
    
    private void updateDepartmentFromDto(Department department, DepartmentDTO dto) {
        department.setName(dto.getName());
        department.setDescription(dto.getDescription());
        department.setFloorNumber(dto.getFloorNumber());
        department.setRoomNumber(dto.getRoomNumber());
    }
} 