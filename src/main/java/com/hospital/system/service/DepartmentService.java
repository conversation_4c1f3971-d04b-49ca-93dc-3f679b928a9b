package com.hospital.system.service;

import com.hospital.system.dto.DepartmentDTO;
import com.hospital.system.model.Department;

import java.util.List;

public interface DepartmentService {
    DepartmentDTO getDepartmentById(Long id);
    
    List<DepartmentDTO> getAllDepartments();
    
    DepartmentDTO createDepartment(DepartmentDTO departmentDTO);
    
    DepartmentDTO updateDepartment(Long id, DepartmentDTO departmentDTO);
    
    void deleteDepartment(Long id);
    
    Department getDepartmentEntityById(Long id);
    
    boolean existsByName(String name);
} 