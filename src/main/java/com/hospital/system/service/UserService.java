package com.hospital.system.service;

import com.hospital.system.dto.RegisterRequest;
import com.hospital.system.dto.UserDTO;
import com.hospital.system.model.User;
import com.hospital.system.model.UserRole;

import java.util.List;

public interface UserService {
    UserDTO register(RegisterRequest registerRequest);
    
    UserDTO getUserById(Long id);
    
    UserDTO updateUser(Long id, UserDTO userDTO);
    
    User getCurrentUser();
    
    UserDTO getCurrentUserDTO();
    
    List<UserDTO> getAllUsers();
    
    void deleteUser(Long id);
    
    boolean checkIfUsernameExists(String username);
    
    boolean checkIfEmailExists(String email);
    
    UserDTO activateUser(Long id);
    
    UserDTO deactivateUser(Long id);
    
    List<UserDTO> getPendingDoctors();
}