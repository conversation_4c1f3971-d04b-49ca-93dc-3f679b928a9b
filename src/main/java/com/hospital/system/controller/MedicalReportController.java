package com.hospital.system.controller;

import com.hospital.system.dto.MedicalReportDTO;
import com.hospital.system.service.MedicalReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;
import java.util.Collections;

@RestController
@RequestMapping("/api/medical-reports")
@CrossOrigin(origins = {"http://localhost:5173", "http://localhost:3000"})
public class MedicalReportController {

    private static final Logger logger = LoggerFactory.getLogger(MedicalReportController.class);

    @Autowired
    private MedicalReportService medicalReportService;

    @GetMapping("/{id}")
    @PreAuthorize("hasAnyRole('ADMIN', 'DOCTOR') or @userSecurity.canAccessMedicalReport(#id)")
    public ResponseEntity<MedicalReportDTO> getMedicalReportById(@PathVariable Long id) {
        return ResponseEntity.ok(medicalReportService.getMedicalReportById(id));
    }

    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<List<MedicalReportDTO>> getAllMedicalReports() {
        return ResponseEntity.ok(medicalReportService.getAllMedicalReports());
    }
    
    @GetMapping("/patient/{patientId}")
    @PreAuthorize("hasAnyRole('ADMIN', 'DOCTOR') or @userSecurity.isPatientSelf(#patientId)")
    public ResponseEntity<List<MedicalReportDTO>> getMedicalReportsByPatient(@PathVariable Long patientId) {
        return ResponseEntity.ok(medicalReportService.getMedicalReportsByPatient(patientId));
    }
    
    @GetMapping("/my-reports")
    @PreAuthorize("hasRole('PATIENT')")
    public ResponseEntity<List<MedicalReportDTO>> getMedicalReportsByCurrentPatient() {
        try {
            List<MedicalReportDTO> reports = medicalReportService.getMedicalReportsByCurrentPatient();
            return ResponseEntity.ok(reports);
        } catch (Exception e) {
            logger.error("获取当前用户医疗报告时发生错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Collections.emptyList());
        }
    }
    
    @GetMapping("/doctor/{doctorId}")
    @PreAuthorize("hasAnyRole('ADMIN', 'DOCTOR') and @userSecurity.canAccessDoctorReports(#doctorId)")
    public ResponseEntity<List<MedicalReportDTO>> getMedicalReportsByDoctor(@PathVariable Long doctorId) {
        return ResponseEntity.ok(medicalReportService.getMedicalReportsByDoctor(doctorId));
    }
    
    @GetMapping("/appointment/{appointmentId}")
    @PreAuthorize("hasAnyRole('ADMIN', 'DOCTOR') or @userSecurity.canAccessAppointment(#appointmentId)")
    public ResponseEntity<List<MedicalReportDTO>> getMedicalReportsByAppointment(@PathVariable Long appointmentId) {
        return ResponseEntity.ok(medicalReportService.getMedicalReportsByAppointment(appointmentId));
    }
    
    @GetMapping("/date-range")
    @PreAuthorize("hasAnyRole('ADMIN', 'DOCTOR')")
    public ResponseEntity<List<MedicalReportDTO>> getMedicalReportsByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        return ResponseEntity.ok(medicalReportService.getMedicalReportsByDateRange(startDate, endDate));
    }

    @PostMapping
    @PreAuthorize("hasRole('DOCTOR')")
    public ResponseEntity<MedicalReportDTO> createMedicalReport(@Valid @RequestBody MedicalReportDTO medicalReportDTO) {
        return ResponseEntity.ok(medicalReportService.createMedicalReport(medicalReportDTO));
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('DOCTOR') and @userSecurity.canUpdateMedicalReport(#id)")
    public ResponseEntity<MedicalReportDTO> updateMedicalReport(
            @PathVariable Long id,
            @Valid @RequestBody MedicalReportDTO medicalReportDTO) {
        return ResponseEntity.ok(medicalReportService.updateMedicalReport(id, medicalReportDTO));
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or (hasRole('DOCTOR') and @userSecurity.canUpdateMedicalReport(#id))")
    public ResponseEntity<Void> deleteMedicalReport(@PathVariable Long id) {
        medicalReportService.deleteMedicalReport(id);
        return ResponseEntity.noContent().build();
    }
} 