package com.hospital.system.controller;

import com.hospital.system.dto.DoctorDTO;
import com.hospital.system.service.DoctorService;
import com.hospital.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/doctors")
@CrossOrigin(origins = {"http://localhost:5173", "http://localhost:3000"})
public class DoctorController {

    @Autowired
    private DoctorService doctorService;
    
    @Autowired
    private UserService userService;

    @GetMapping("/profile")
    @PreAuthorize("hasRole('DOCTOR')")
    public ResponseEntity<DoctorDTO> getCurrentDoctorProfile() {
        return ResponseEntity.ok(doctorService.getCurrentDoctor());
    }

    @GetMapping("/{id}")
    public ResponseEntity<DoctorDTO> getDoctorById(@PathVariable Long id) {
        return ResponseEntity.ok(doctorService.getDoctorById(id));
    }

    @GetMapping
    public ResponseEntity<List<DoctorDTO>> getAllDoctors() {
        return ResponseEntity.ok(doctorService.getAllDoctors());
    }
    
    @GetMapping("/department/{departmentId}")
    public ResponseEntity<List<DoctorDTO>> getDoctorsByDepartment(@PathVariable Long departmentId) {
        return ResponseEntity.ok(doctorService.getDoctorsByDepartment(departmentId));
    }
    
    @GetMapping("/top-rated")
    public ResponseEntity<List<DoctorDTO>> getTopRatedDoctors(@RequestParam(defaultValue = "5") int limit) {
        return ResponseEntity.ok(doctorService.getTopRatedDoctors(limit));
    }

    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<DoctorDTO> createDoctor(@Valid @RequestBody DoctorDTO doctorDTO) {
        return ResponseEntity.ok(doctorService.createDoctor(doctorDTO, userService.getCurrentUser()));
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or @userSecurity.isDoctorSelf(#id)")
    public ResponseEntity<DoctorDTO> updateDoctor(@PathVariable Long id, @Valid @RequestBody DoctorDTO doctorDTO) {
        return ResponseEntity.ok(doctorService.updateDoctor(id, doctorDTO));
    }
    
    @PutMapping("/{id}/rating")
    @PreAuthorize("hasRole('PATIENT')")
    public ResponseEntity<DoctorDTO> rateDoctorr(@PathVariable Long id, @RequestParam Integer rating) {
        return ResponseEntity.ok(doctorService.updateDoctorRating(id, rating));
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteDoctor(@PathVariable Long id) {
        doctorService.deleteDoctor(id);
        return ResponseEntity.noContent().build();
    }

    @PostMapping("/{id}/certificate")
    @PreAuthorize("hasRole('ADMIN') or @userSecurity.isDoctorSelf(#id)")
    public ResponseEntity<?> uploadCertificate(@PathVariable Long id, @RequestParam("file") MultipartFile file) {
        try {
            String filePath = doctorService.uploadCertificate(id, file);
            Map<String, String> response = new HashMap<>();
            response.put("certificatePath", filePath);
            response.put("message", "资质证书上传成功");
            return ResponseEntity.ok(response);
        } catch (IOException e) {
            return ResponseEntity.badRequest().body("文件上传失败: " + e.getMessage());
        }
    }
    
    @PostMapping("/{id}/qualification")
    @PreAuthorize("hasRole('ADMIN') or @userSecurity.isDoctorSelf(#id)")
    public ResponseEntity<DoctorDTO> updateQualification(
            @PathVariable Long id,
            @RequestParam(required = false) String resume,
            @RequestParam(required = false) String educationBackground,
            @RequestParam(required = false) String professionalTitle) {
        
        return ResponseEntity.ok(doctorService.updateQualification(id, resume, educationBackground, professionalTitle));
    }
} 