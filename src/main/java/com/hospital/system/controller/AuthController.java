package com.hospital.system.controller;

import com.hospital.system.dto.JwtRequest;
import com.hospital.system.dto.JwtResponse;
import com.hospital.system.dto.RegisterRequest;
import com.hospital.system.dto.UserDTO;
import com.hospital.system.model.UserRole;
import com.hospital.system.security.JwtTokenUtil;
import com.hospital.system.security.JwtUserDetailsService;
import com.hospital.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/auth")
@CrossOrigin(origins = {"http://localhost:5173", "http://localhost:3000"})
public class AuthController {

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private JwtTokenUtil jwtTokenUtil;

    @Autowired
    private JwtUserDetailsService userDetailsService;
    
    @Autowired
    private UserService userService;

    /**
     * 用户登录接口
     *
     * @param authenticationRequest 包含用户名和密码的请求对象
     * @return 返回包含JWT令牌的响应对象
     * @throws Exception 如果用户名或密码无效，或者用户被禁用，则抛出异常
     */
    @PostMapping("/login")
    public ResponseEntity<?> createAuthenticationToken(@RequestBody JwtRequest authenticationRequest) throws Exception {
        authenticate(authenticationRequest.getUsername(), authenticationRequest.getPassword());

        final UserDetails userDetails = userDetailsService
                .loadUserByUsername(authenticationRequest.getUsername());

        final String token = jwtTokenUtil.generateToken(userDetails);

        return ResponseEntity.ok(new JwtResponse(token));
    }

    /**
     * 用户注册接口
     *
     * @param registerRequest 包含注册信息的请求对象
     * @return 返回包含用户信息的响应对象，如果是医生注册，则附带审核提示信息
     */
    @PostMapping("/register")
    public ResponseEntity<?> registerUser(@Valid @RequestBody RegisterRequest registerRequest) {
        UserDTO userDTO = userService.register(registerRequest);
        
        // 如果是医生注册，返回需要审核的提示
        if (registerRequest.getRole() == UserRole.DOCTOR) {
            Map<String, Object> response = new HashMap<>();
            response.put("user", userDTO);
            response.put("message", "医生账号注册成功，需要管理员审核后才能使用");
            return ResponseEntity.ok(response);
        }
        
        return ResponseEntity.ok(userDTO);
    }

    private void authenticate(String username, String password) throws Exception {
        try {
            authenticationManager.authenticate(new UsernamePasswordAuthenticationToken(username, password));
        } catch (DisabledException e) {
            throw new Exception("USER_DISABLED", e);
        } catch (BadCredentialsException e) {
            throw new Exception("INVALID_CREDENTIALS", e);
        }
    }
}
