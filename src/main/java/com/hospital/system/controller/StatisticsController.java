package com.hospital.system.controller;

import com.hospital.system.dto.StatsResponse;
import com.hospital.system.service.StatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.Map;

@RestController
@RequestMapping("/api/statistics")
@CrossOrigin(origins = {"http://localhost:5173", "http://localhost:3000"})
public class StatisticsController {

    @Autowired
    private StatisticsService statisticsService;

    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<StatsResponse> getSystemStatistics() {
        return ResponseEntity.ok(statisticsService.getSystemStatistics());
    }
    
    @GetMapping("/doctor-workload")
    @PreAuthorize("hasAnyRole('ADMIN', 'DOCTOR')")
    public ResponseEntity<StatsResponse> getDoctorWorkloadStatistics(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        return ResponseEntity.ok(statisticsService.getDoctorWorkloadStatistics(startDate, endDate));
    }
    
    @GetMapping("/department-popularity")
    @PreAuthorize("hasAnyRole('ADMIN', 'DOCTOR')")
    public ResponseEntity<StatsResponse> getDepartmentPopularityStatistics(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        return ResponseEntity.ok(statisticsService.getDepartmentPopularityStatistics(startDate, endDate));
    }
    
    @GetMapping("/doctor/{doctorId}")
    @PreAuthorize("hasAnyRole('ADMIN', 'DOCTOR') and @userSecurity.canAccessDoctorStatistics(#doctorId)")
    public ResponseEntity<StatsResponse> getDoctorStatistics(@PathVariable Long doctorId) {
        return ResponseEntity.ok(statisticsService.getDoctorStatistics(doctorId));
    }
    
    @GetMapping("/department/{departmentId}")
    @PreAuthorize("hasAnyRole('ADMIN', 'DOCTOR')")
    public ResponseEntity<StatsResponse> getDepartmentStatistics(@PathVariable Long departmentId) {
        return ResponseEntity.ok(statisticsService.getDepartmentStatistics(departmentId));
    }
    
    @GetMapping("/monthly")
    @PreAuthorize("hasAnyRole('ADMIN', 'DOCTOR')")
    public ResponseEntity<Map<String, Integer>> getMonthlyAppointmentStatistics(
            @RequestParam(defaultValue = "2023") int year) {
        return ResponseEntity.ok(statisticsService.getMonthlyAppointmentStatistics(year));
    }
    
    @GetMapping("/monthly/doctor/{doctorId}")
    @PreAuthorize("hasAnyRole('ADMIN', 'DOCTOR') and @userSecurity.canAccessDoctorStatistics(#doctorId)")
    public ResponseEntity<Map<String, Integer>> getMonthlyAppointmentStatisticsByDoctor(
            @PathVariable Long doctorId,
            @RequestParam(defaultValue = "2023") int year) {
        return ResponseEntity.ok(statisticsService.getMonthlyAppointmentStatisticsByDoctor(doctorId, year));
    }
    
    @GetMapping("/monthly/department/{departmentId}")
    @PreAuthorize("hasAnyRole('ADMIN', 'DOCTOR')")
    public ResponseEntity<Map<String, Integer>> getMonthlyAppointmentStatisticsByDepartment(
            @PathVariable Long departmentId,
            @RequestParam(defaultValue = "2023") int year) {
        return ResponseEntity.ok(statisticsService.getMonthlyAppointmentStatisticsByDepartment(departmentId, year));
    }
} 