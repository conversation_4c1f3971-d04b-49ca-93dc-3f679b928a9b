package com.hospital.system.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.LocalDate;
import java.time.LocalTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "appointments")
public class Appointment {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "appointment_date")
    private LocalDate appointmentDate;
    
    @Column(name = "start_time")
    private LocalTime startTime;
    
    @Column(name = "end_time")
    private LocalTime endTime;
    
    @Column(name = "appointment_number", unique = true)
    private String appointmentNumber;
    
    @Enumerated(EnumType.STRING)
    private AppointmentStatus status;
    
    @Column(columnDefinition = "TEXT")
    private String symptoms;
    
    @Column(columnDefinition = "TEXT", name = "diagnosis_notes")
    private String diagnosisNotes;
    
    @Column(name = "created_at")
    private LocalDate createdAt;
    
    @ManyToOne
    @JoinColumn(name = "patient_id", nullable = false)
    private Patient patient;
    
    @ManyToOne
    @JoinColumn(name = "doctor_id", nullable = false)
    private Doctor doctor;
    
    @ManyToOne
    @JoinColumn(name = "schedule_id")
    private Schedule schedule;
    
    @OneToMany(mappedBy = "appointment", cascade = CascadeType.ALL)
    private java.util.List<MedicalReport> medicalReports = new java.util.ArrayList<>();
    
    @PrePersist
    protected void onCreate() {
        this.createdAt = LocalDate.now();
    }
} 