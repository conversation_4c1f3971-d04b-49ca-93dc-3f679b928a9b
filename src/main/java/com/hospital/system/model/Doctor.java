package com.hospital.system.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "doctors")
public class Doctor {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    private String specialization;
    
    @Column(name = "license_number")
    private String licenseNumber;
    
    private String qualification;
    
    @Column(name = "years_of_experience")
    private Integer yearsOfExperience;
    
    @Column(name = "certificate_path")
    private String certificatePath;
    
    @Column(name = "resume", columnDefinition = "TEXT")
    private String resume;
    
    @Column(name = "education_background", columnDefinition = "TEXT")
    private String educationBackground;
    
    @Column(name = "professional_title")
    private String professionalTitle;
    
    @OneToOne
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    @ManyToOne
    @JoinColumn(name = "department_id", nullable = false)
    private Department department;
    
    @OneToMany(mappedBy = "doctor")
    private List<Schedule> schedules = new ArrayList<>();
    
    @OneToMany(mappedBy = "doctor")
    private List<Appointment> appointments = new ArrayList<>();
    
    @Column(name = "rating_score")
    private Double ratingScore;
    
    @Column(name = "rating_count")
    private Integer ratingCount;
} 