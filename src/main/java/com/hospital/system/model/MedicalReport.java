package com.hospital.system.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.LocalDate;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "medical_reports")
public class MedicalReport {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "report_type")
    private String reportType;
    
    @Column(name = "report_date")
    private LocalDate reportDate;
    
    @Column(columnDefinition = "TEXT")
    private String results;
    
    @Column(name = "file_path")
    private String filePath;
    
    @ManyToOne
    @JoinColumn(name = "appointment_id", nullable = false)
    private Appointment appointment;
    
    @PrePersist
    protected void onCreate() {
        this.reportDate = LocalDate.now();
    }
} 