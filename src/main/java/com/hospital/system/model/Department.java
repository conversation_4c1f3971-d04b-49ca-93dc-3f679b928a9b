package com.hospital.system.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "departments")
public class Department {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false, unique = true)
    private String name;
    
    private String description;
    
    @Column(name = "floor_number")
    private Integer floorNumber;
    
    @Column(name = "room_number")
    private String roomNumber;
    
    @OneToMany(mappedBy = "department")
    private List<Doctor> doctors = new ArrayList<>();
} 