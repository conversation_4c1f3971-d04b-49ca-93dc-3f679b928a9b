package com.hospital.system.repository;

import com.hospital.system.model.MedicalReport;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface MedicalReportRepository extends JpaRepository<MedicalReport, Long> {
    List<MedicalReport> findByAppointmentId(Long appointmentId);
    
    @Query("SELECT mr FROM MedicalReport mr WHERE mr.appointment.patient.id = :patientId ORDER BY mr.reportDate DESC")
    List<MedicalReport> findByPatientId(Long patientId);
    
    @Query("SELECT mr FROM MedicalReport mr WHERE mr.appointment.doctor.id = :doctorId ORDER BY mr.reportDate DESC")
    List<MedicalReport> findByDoctorId(Long doctorId);
    
    List<MedicalReport> findByReportDateBetween(LocalDate startDate, LocalDate endDate);
    
    @Query("SELECT mr FROM MedicalReport mr WHERE mr.reportType = :reportType")
    List<MedicalReport> findByReportType(String reportType);
} 