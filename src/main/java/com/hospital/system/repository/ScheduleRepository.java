package com.hospital.system.repository;

import com.hospital.system.model.Doctor;
import com.hospital.system.model.Schedule;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.DayOfWeek;
import java.util.List;

@Repository
public interface ScheduleRepository extends JpaRepository<Schedule, Long> {
    List<Schedule> findByDoctor(Doctor doctor);
    
    List<Schedule> findByDoctorId(Long doctorId);
    
    List<Schedule> findByDoctorIdAndDayOfWeek(Long doctorId, DayOfWeek dayOfWeek);
    
    @Query("SELECT s FROM Schedule s WHERE s.doctor.department.id = :departmentId")
    List<Schedule> findByDepartmentId(Long departmentId);
    
    @Query("SELECT s FROM Schedule s WHERE s.doctor.id = :doctorId AND " +
           "s.dayOfWeek = :dayOfWeek AND s.maxAppointments > " +
           "(SELECT COUNT(a) FROM Appointment a WHERE a.schedule.id = s.id AND a.status = 'SCHEDULED')")
    List<Schedule> findAvailableSchedules(Long doctorId, DayOfWeek dayOfWeek);
} 