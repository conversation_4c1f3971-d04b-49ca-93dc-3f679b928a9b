package com.hospital.system.repository;

import com.hospital.system.model.Appointment;
import com.hospital.system.model.AppointmentStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface AppointmentRepository extends JpaRepository<Appointment, Long> {
    List<Appointment> findByPatientId(Long patientId);
    
    List<Appointment> findByDoctorId(Long doctorId);
    
    List<Appointment> findByDoctorIdAndAppointmentDate(Long doctorId, LocalDate appointmentDate);
    
    List<Appointment> findByPatientIdAndStatus(Long patientId, AppointmentStatus status);
    
    List<Appointment> findByDoctorIdAndStatus(Long doctorId, AppointmentStatus status);
    
    Optional<Appointment> findByAppointmentNumber(String appointmentNumber);
    
    @Query("SELECT a FROM Appointment a WHERE a.doctor.department.id = :departmentId")
    List<Appointment> findByDepartmentId(Long departmentId);
    
    @Query("SELECT COUNT(a) FROM Appointment a WHERE a.doctor.id = :doctorId AND a.appointmentDate = :date")
    Integer countAppointmentsByDoctorAndDate(Long doctorId, LocalDate date);
    
    @Query("SELECT a FROM Appointment a WHERE a.appointmentDate = :date ORDER BY a.startTime")
    List<Appointment> findAppointmentsByDate(LocalDate date);
    
    @Query("SELECT COUNT(a) FROM Appointment a WHERE a.doctor.id = :doctorId AND a.status = 'COMPLETED' AND a.appointmentDate BETWEEN :startDate AND :endDate")
    Integer countCompletedAppointmentsByDoctor(Long doctorId, LocalDate startDate, LocalDate endDate);
    
    @Query("SELECT a.doctor.id, COUNT(a) FROM Appointment a WHERE a.status = 'COMPLETED' AND a.appointmentDate BETWEEN :startDate AND :endDate GROUP BY a.doctor.id ORDER BY COUNT(a) DESC")
    List<Object[]> findDoctorWorkloadStatistics(LocalDate startDate, LocalDate endDate);
    
    @Query("SELECT a.doctor.department.id, COUNT(a) FROM Appointment a WHERE a.appointmentDate BETWEEN :startDate AND :endDate GROUP BY a.doctor.department.id ORDER BY COUNT(a) DESC")
    List<Object[]> findDepartmentPopularityStatistics(LocalDate startDate, LocalDate endDate);
} 