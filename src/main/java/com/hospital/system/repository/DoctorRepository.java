package com.hospital.system.repository;

import com.hospital.system.model.Department;
import com.hospital.system.model.Doctor;
import com.hospital.system.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface DoctorRepository extends JpaRepository<Doctor, Long> {
    Optional<Doctor> findByUser(User user);
    
    List<Doctor> findByDepartment(Department department);
    
    @Query("SELECT d FROM Doctor d WHERE d.department.id = :departmentId")
    List<Doctor> findByDepartmentId(Long departmentId);
    
    @Query("SELECT d FROM Doctor d ORDER BY d.ratingScore DESC")
    List<Doctor> findTopRatedDoctors();
    
    boolean existsByLicenseNumber(String licenseNumber);
} 