package com.hospital.system.security;

import com.hospital.system.model.Doctor;
import com.hospital.system.model.Patient;
import com.hospital.system.model.User;
import com.hospital.system.repository.DoctorRepository;
import com.hospital.system.repository.PatientRepository;
import com.hospital.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component("userSecurity")
public class UserSecurity {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private DoctorRepository doctorRepository;
    
    @Autowired
    private PatientRepository patientRepository;
    
    public boolean isUserSelf(Long userId) {
        try {
            User currentUser = userService.getCurrentUser();
            return currentUser.getId().equals(userId);
        } catch (Exception e) {
            return false;
        }
    }
    
    public boolean isDoctorSelf(Long doctorId) {
        try {
            User currentUser = userService.getCurrentUser();
            return doctorRepository.findById(doctorId)
                    .map(doctor -> doctor.getUser().getId().equals(currentUser.getId()))
                    .orElse(false);
        } catch (Exception e) {
            return false;
        }
    }
    
    public boolean isPatientSelf(Long patientId) {
        try {
            User currentUser = userService.getCurrentUser();
            return patientRepository.findById(patientId)
                    .map(patient -> patient.getUser().getId().equals(currentUser.getId()))
                    .orElse(false);
        } catch (Exception e) {
            return false;
        }
    }
} 