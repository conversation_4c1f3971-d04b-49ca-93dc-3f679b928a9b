package com.hospital.system.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DoctorDTO {
    private Long id;
    private Long userId;
    private Long departmentId;
    private String departmentName;
    private String specialization;
    private String licenseNumber;
    private String qualification;
    private Integer yearsOfExperience;
    private Double ratingScore;
    private Integer ratingCount;
    private String fullName;
    private String phoneNumber;
    private String email;
    
    // 新增资质信息字段
    private String certificatePath;
    private String resume;
    private String educationBackground;
    private String professionalTitle;
}