package com.hospital.system.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StatsResponse {
    private Integer totalPatients;
    private Integer totalDoctors;
    private Integer totalAppointments;
    private Integer totalDepartments;
    private Integer todayAppointments;
    private Integer completedAppointments;
    private Integer cancelledAppointments;
    
    // 科室预约热度排行
    private List<DepartmentStatsDTO> departmentStats;
    
    // 医生工作量统计
    private List<DoctorStatsDTO> doctorStats;
    
    // 按月统计数据
    private Map<String, Integer> monthlyAppointments;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DepartmentStatsDTO {
        private Long departmentId;
        private String departmentName;
        private Integer appointmentCount;
        private Integer doctorCount;
        private Double trendPercentage;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DoctorStatsDTO {
        private Long doctorId;
        private String doctorName;
        private String departmentName;
        private Integer appointmentCount;
        private Double ratingScore;
        private Integer ratingCount;
    }
} 