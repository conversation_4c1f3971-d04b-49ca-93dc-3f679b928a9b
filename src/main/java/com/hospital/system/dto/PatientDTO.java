package com.hospital.system.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PatientDTO {
    private Long id;
    private Long userId;
    private String medicalHistory;
    private Date birthDate;
    private String gender;
    private String address;
    private String emergencyContact;
    private String fullName;
    private String phoneNumber;
    private String email;
} 