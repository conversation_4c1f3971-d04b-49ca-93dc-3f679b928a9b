package com.hospital.system.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DiagnosisRequest {
    @NotNull(message = "预约ID不能为空")
    private Long appointmentId;
    
    @NotBlank(message = "诊断结果不能为空")
    private String diagnosisNotes;
    
    private String reportType;
    
    private String reportResults;
} 